"""
交易機器人主模組
整合所有交易邏輯和風險管理
"""
import time
from typing import Optional, Dict, Any
import pandas as pd

from config import config
from logger import get_logger
from api_client import MEXCAPIClient, APIError
from indicators import TechnicalIndicators
from position_manager import PositionManager, PositionStatus


class TradingBot:
    """交易機器人"""
    
    def __init__(self):
        self.logger = get_logger("trading_bot")
        self.api_client = MEXCAPIClient()
        self.indicators = TechnicalIndicators()
        self.position_manager = PositionManager()
        
        self.logger.info("交易機器人初始化完成")
        
        # 驗證配置
        if not config.validate():
            raise ValueError("配置驗證失敗")
    
    def get_market_data(self) -> Optional[pd.DataFrame]:
        """獲取市場數據並計算技術指標"""
        try:
            # 獲取K線數據
            df = self.api_client.get_klines()
            
            if df is None or len(df) == 0:
                self.logger.error("未獲取到市場數據")
                return None
            
            # 計算布林帶
            df = self.indicators.bollinger_bands(df)
            
            # 記錄市場數據
            current_data = df.iloc[-1]
            self.logger.market_data(
                symbol=config.trading.symbol,
                price=current_data["close"],
                bbu=current_data["BBU"],
                bbl=current_data["BBL"]
            )
            
            return df
            
        except Exception as e:
            self.logger.error(f"獲取市場數據失敗: {str(e)}")
            return None
    
    def check_exit_signals(self, current_price: float) -> bool:
        """檢查平倉信號"""
        if self.position_manager.position.status == PositionStatus.NO_POSITION:
            return False
        
        exit_reason = self.position_manager.check_exit_conditions(current_price)
        
        if exit_reason:
            try:
                # 執行平倉
                side = "SELL" if self.position_manager.position.status == PositionStatus.LONG else "BUY"
                
                result = self.api_client.place_order(
                    side=side,
                    quantity=self.position_manager.position.size,
                    symbol=self.position_manager.position.symbol
                )
                
                # 更新倉位狀態
                self.position_manager.close_position(current_price, exit_reason)
                
                self.logger.info(f"{exit_reason}平倉成功: {result}")
                return True
                
            except Exception as e:
                self.logger.error(f"平倉失敗: {str(e)}")
                return False
        
        return False
    
    def check_entry_signals(self, df: pd.DataFrame) -> bool:
        """檢查開倉信號"""
        if self.position_manager.position.status != PositionStatus.NO_POSITION:
            return False
        
        try:
            # 檢查布林帶信號
            signal = self.indicators.check_bollinger_signal(df)
            current_price = signal["current_price"]
            
            if signal["long"]:
                # 執行做多開倉
                size = self.position_manager.calculate_position_size(current_price)
                
                result = self.api_client.place_order(
                    side="BUY",
                    quantity=size,
                    symbol=config.trading.symbol
                )
                
                # 更新倉位狀態
                self.position_manager.open_position(
                    side="BUY",
                    entry_price=current_price,
                    size=size,
                    symbol=config.trading.symbol
                )
                
                self.logger.info(f"做多開倉成功: {result}")
                return True
                
            elif signal["short"]:
                # 做空邏輯（目前註解掉，可以根據需要啟用）
                self.logger.info("檢測到做空信號，但目前未啟用做空交易")
                pass
            
            return False
            
        except Exception as e:
            self.logger.error(f"檢查開倉信號失敗: {str(e)}")
            return False
    
    def run_trading_cycle(self) -> Dict[str, Any]:
        """執行一次交易週期"""
        cycle_start_time = time.time()
        
        try:
            # 獲取市場數據
            df = self.get_market_data()
            if df is None:
                return {"success": False, "error": "無法獲取市場數據"}
            
            current_price = df.iloc[-1]["close"]
            
            # 檢查平倉條件
            exit_executed = self.check_exit_signals(current_price)
            
            # 檢查開倉條件（只有在沒有倉位時）
            entry_executed = False
            if not exit_executed:
                entry_executed = self.check_entry_signals(df)
            
            # 獲取當前狀態
            status = self.position_manager.get_status_summary()
            
            cycle_time = time.time() - cycle_start_time
            
            # 輸出狀態資訊
            current_data = df.iloc[-1]
            self.logger.info(
                f"價格: {int(current_price)} | "
                f"上軌: {int(current_data['BBU'])} | "
                f"下軌: {int(current_data['BBL'])} | "
                f"倉位: {status['position']['status']} | "
                f"總獲利: {status['stats']['total_profit']:.2f} | "
                f"勝率: {status['stats']['win_rate']:.1f}% | "
                f"週期耗時: {cycle_time:.3f}秒"
            )
            
            return {
                "success": True,
                "current_price": current_price,
                "position_status": status['position']['status'],
                "total_profit": status['stats']['total_profit'],
                "win_rate": status['stats']['win_rate'],
                "entry_executed": entry_executed,
                "exit_executed": exit_executed,
                "cycle_time": cycle_time
            }
            
        except Exception as e:
            self.logger.error(f"交易週期執行失敗: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """獲取績效摘要"""
        return self.position_manager.get_status_summary()
    
    def emergency_close_all(self) -> bool:
        """緊急平倉所有倉位"""
        try:
            if self.position_manager.position.status == PositionStatus.NO_POSITION:
                self.logger.info("無倉位需要緊急平倉")
                return True
            
            # 獲取當前價格
            df = self.get_market_data()
            if df is None:
                self.logger.error("無法獲取當前價格進行緊急平倉")
                return False
            
            current_price = df.iloc[-1]["close"]
            
            # 執行平倉
            side = "SELL" if self.position_manager.position.status == PositionStatus.LONG else "BUY"
            
            result = self.api_client.place_order(
                side=side,
                quantity=self.position_manager.position.size,
                symbol=self.position_manager.position.symbol
            )
            
            # 更新倉位狀態
            self.position_manager.close_position(current_price, "緊急平倉")
            
            self.logger.critical(f"緊急平倉完成: {result}")
            return True
            
        except Exception as e:
            self.logger.critical(f"緊急平倉失敗: {str(e)}")
            return False
