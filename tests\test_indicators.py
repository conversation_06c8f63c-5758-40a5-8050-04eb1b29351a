"""
技術指標模組測試
"""
import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch

import sys
sys.path.append('../src')

from src.indicators import TechnicalIndicators


class TestTechnicalIndicators:
    """技術指標測試"""
    
    def setup_method(self):
        """測試前設置"""
        self.indicators = TechnicalIndicators()
        
        # 創建測試數據
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='1min')
        prices = 50000 + np.cumsum(np.random.randn(100) * 100)
        
        self.test_df = pd.DataFrame({
            'timestamp': dates,
            'close': prices,
            'high': prices + np.random.rand(100) * 50,
            'low': prices - np.random.rand(100) * 50,
            'volume': np.random.rand(100) * 1000
        })
    
    def test_bollinger_bands_calculation(self):
        """測試布林帶計算"""
        result_df = self.indicators.bollinger_bands(self.test_df.copy(), period=20, std_dev=2)
        
        # 檢查是否添加了必要的欄位
        assert 'BBM' in result_df.columns
        assert 'BBU' in result_df.columns
        assert 'BBL' in result_df.columns
        assert 'std' in result_df.columns
        
        # 檢查數值合理性
        assert result_df['BBU'].iloc[-1] > result_df['BBM'].iloc[-1]
        assert result_df['BBL'].iloc[-1] < result_df['BBM'].iloc[-1]
        assert result_df['BBU'].iloc[-1] > result_df['BBL'].iloc[-1]
    
    def test_bollinger_bands_insufficient_data(self):
        """測試數據不足時的錯誤處理"""
        short_df = self.test_df.head(10)
        
        with pytest.raises(ValueError, match="數據長度.*小於所需週期"):
            self.indicators.bollinger_bands(short_df, period=20)
    
    def test_bollinger_bands_missing_column(self):
        """測試缺少價格欄位時的錯誤處理"""
        df_without_close = self.test_df.drop('close', axis=1)
        
        with pytest.raises(ValueError, match="DataFrame中不存在欄位"):
            self.indicators.bollinger_bands(df_without_close)
    
    def test_moving_average_sma(self):
        """測試簡單移動平均"""
        ma = self.indicators.moving_average(self.test_df, period=10, ma_type="SMA")
        
        # 檢查長度
        assert len(ma) == len(self.test_df)
        
        # 檢查前幾個值是NaN（因為數據不足）
        assert pd.isna(ma.iloc[:9]).all()
        
        # 檢查第10個值是前10個值的平均
        expected = self.test_df['close'].iloc[:10].mean()
        assert abs(ma.iloc[9] - expected) < 1e-10
    
    def test_moving_average_ema(self):
        """測試指數移動平均"""
        ema = self.indicators.moving_average(self.test_df, period=10, ma_type="EMA")
        
        # 檢查長度
        assert len(ema) == len(self.test_df)
        
        # EMA應該沒有NaN值（除了第一個）
        assert not pd.isna(ema.iloc[1:]).any()
    
    def test_rsi_calculation(self):
        """測試RSI計算"""
        rsi = self.indicators.rsi(self.test_df, period=14)
        
        # 檢查長度
        assert len(rsi) == len(self.test_df)
        
        # 檢查RSI值在0-100範圍內
        valid_rsi = rsi.dropna()
        assert (valid_rsi >= 0).all()
        assert (valid_rsi <= 100).all()
    
    def test_macd_calculation(self):
        """測試MACD計算"""
        macd_line, signal_line, histogram = self.indicators.macd(self.test_df)
        
        # 檢查長度
        assert len(macd_line) == len(self.test_df)
        assert len(signal_line) == len(self.test_df)
        assert len(histogram) == len(self.test_df)
        
        # 檢查柱狀圖 = MACD線 - 信號線
        diff = macd_line - signal_line - histogram
        assert abs(diff.dropna()).max() < 1e-10
    
    def test_check_bollinger_signal_long(self):
        """測試布林帶做多信號"""
        # 創建觸發做多信號的數據
        df = self.test_df.copy()
        df = self.indicators.bollinger_bands(df)
        
        # 手動設置最後兩根K線低於下軌
        df.loc[df.index[-2:], 'close'] = df.loc[df.index[-2:], 'BBL'] - 100
        
        signal = self.indicators.check_bollinger_signal(df, lookback=2)
        
        assert signal['long'] is True
        assert signal['short'] is False
        assert signal['message'] == "觸發做多信號"
    
    def test_check_bollinger_signal_short(self):
        """測試布林帶做空信號"""
        # 創建觸發做空信號的數據
        df = self.test_df.copy()
        df = self.indicators.bollinger_bands(df)
        
        # 手動設置最後兩根K線高於上軌
        df.loc[df.index[-2:], 'close'] = df.loc[df.index[-2:], 'BBU'] + 100
        
        signal = self.indicators.check_bollinger_signal(df, lookback=2)
        
        assert signal['long'] is False
        assert signal['short'] is True
        assert signal['message'] == "觸發做空信號"
    
    def test_check_bollinger_signal_no_signal(self):
        """測試無交易信號"""
        df = self.test_df.copy()
        df = self.indicators.bollinger_bands(df)
        
        signal = self.indicators.check_bollinger_signal(df, lookback=2)
        
        assert signal['long'] is False
        assert signal['short'] is False
        assert signal['message'] == "無交易信號"
    
    def test_check_bollinger_signal_insufficient_data(self):
        """測試數據不足時的處理"""
        df = self.test_df.head(1)
        df = self.indicators.bollinger_bands(df, period=1)
        
        signal = self.indicators.check_bollinger_signal(df, lookback=2)
        
        assert signal['long'] is False
        assert signal['short'] is False
        assert signal['message'] == "數據不足"


if __name__ == '__main__':
    pytest.main([__file__])
