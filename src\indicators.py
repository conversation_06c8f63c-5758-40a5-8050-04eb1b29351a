"""
技術指標計算模組
提供各種技術分析指標的計算功能
"""
import pandas as pd
import numpy as np
from typing import Tuple, Optional

from config import config
from logger import get_logger


class TechnicalIndicators:
    """技術指標計算器"""
    
    def __init__(self):
        self.logger = get_logger("indicators")
    
    def bollinger_bands(self, df: pd.DataFrame, period: int = None, 
                       std_dev: float = None, price_column: str = "close") -> pd.DataFrame:
        """
        計算布林帶指標
        
        Args:
            df: 包含價格數據的DataFrame
            period: 移動平均週期
            std_dev: 標準差倍數
            price_column: 價格欄位名稱
            
        Returns:
            添加了布林帶指標的DataFrame
        """
        period = period or config.trading.bollinger_period
        std_dev = std_dev or config.trading.bollinger_std_dev
        
        try:
            if price_column not in df.columns:
                raise ValueError(f"DataFrame中不存在欄位: {price_column}")
            
            if len(df) < period:
                raise ValueError(f"數據長度({len(df)})小於所需週期({period})")
            
            # 計算中軌（移動平均線）
            df["BBM"] = df[price_column].rolling(window=period).mean()
            
            # 計算標準差
            df["std"] = df[price_column].rolling(window=period).std()
            
            # 計算上軌和下軌
            df["BBU"] = df["BBM"] + (df["std"] * std_dev)
            df["BBL"] = df["BBM"] - (df["std"] * std_dev)
            
            self.logger.debug(f"成功計算布林帶: 週期={period}, 標準差倍數={std_dev}")
            return df
            
        except Exception as e:
            self.logger.error(f"布林帶計算失敗: {str(e)}")
            raise
    
    def moving_average(self, df: pd.DataFrame, period: int, 
                      price_column: str = "close", ma_type: str = "SMA") -> pd.Series:
        """
        計算移動平均線
        
        Args:
            df: 包含價格數據的DataFrame
            period: 移動平均週期
            price_column: 價格欄位名稱
            ma_type: 移動平均類型 ("SMA", "EMA")
            
        Returns:
            移動平均線數據
        """
        try:
            if price_column not in df.columns:
                raise ValueError(f"DataFrame中不存在欄位: {price_column}")
            
            if ma_type.upper() == "SMA":
                return df[price_column].rolling(window=period).mean()
            elif ma_type.upper() == "EMA":
                return df[price_column].ewm(span=period).mean()
            else:
                raise ValueError(f"不支援的移動平均類型: {ma_type}")
                
        except Exception as e:
            self.logger.error(f"移動平均計算失敗: {str(e)}")
            raise
    
    def rsi(self, df: pd.DataFrame, period: int = 14, 
           price_column: str = "close") -> pd.Series:
        """
        計算相對強弱指標(RSI)
        
        Args:
            df: 包含價格數據的DataFrame
            period: RSI週期
            price_column: 價格欄位名稱
            
        Returns:
            RSI數據
        """
        try:
            if price_column not in df.columns:
                raise ValueError(f"DataFrame中不存在欄位: {price_column}")
            
            # 計算價格變化
            delta = df[price_column].diff()
            
            # 分離上漲和下跌
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            
            # 計算平均收益和平均損失
            avg_gain = gain.rolling(window=period).mean()
            avg_loss = loss.rolling(window=period).mean()
            
            # 計算RS和RSI
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            self.logger.debug(f"成功計算RSI: 週期={period}")
            return rsi
            
        except Exception as e:
            self.logger.error(f"RSI計算失敗: {str(e)}")
            raise
    
    def macd(self, df: pd.DataFrame, fast_period: int = 12, slow_period: int = 26, 
            signal_period: int = 9, price_column: str = "close") -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        計算MACD指標
        
        Args:
            df: 包含價格數據的DataFrame
            fast_period: 快線週期
            slow_period: 慢線週期
            signal_period: 信號線週期
            price_column: 價格欄位名稱
            
        Returns:
            (MACD線, 信號線, 柱狀圖)
        """
        try:
            if price_column not in df.columns:
                raise ValueError(f"DataFrame中不存在欄位: {price_column}")
            
            # 計算快線和慢線EMA
            ema_fast = df[price_column].ewm(span=fast_period).mean()
            ema_slow = df[price_column].ewm(span=slow_period).mean()
            
            # 計算MACD線
            macd_line = ema_fast - ema_slow
            
            # 計算信號線
            signal_line = macd_line.ewm(span=signal_period).mean()
            
            # 計算柱狀圖
            histogram = macd_line - signal_line
            
            self.logger.debug(f"成功計算MACD: 快線={fast_period}, 慢線={slow_period}, 信號線={signal_period}")
            return macd_line, signal_line, histogram
            
        except Exception as e:
            self.logger.error(f"MACD計算失敗: {str(e)}")
            raise
    
    def check_bollinger_signal(self, df: pd.DataFrame, lookback: int = 2) -> dict:
        """
        檢查布林帶交易信號
        
        Args:
            df: 包含布林帶數據的DataFrame
            lookback: 回看週期數
            
        Returns:
            包含交易信號的字典
        """
        try:
            required_columns = ["close", "BBU", "BBL"]
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"DataFrame中缺少必要欄位: {col}")
            
            if len(df) < lookback:
                return {"long": False, "short": False, "message": "數據不足"}
            
            # 檢查做多信號：連續幾根K線收盤價都低於下軌
            long_condition = all(
                df.iloc[-(i+1)]["close"] < df.iloc[-(i+1)]["BBL"] 
                for i in range(lookback)
            )
            
            # 檢查做空信號：連續幾根K線收盤價都高於上軌
            short_condition = all(
                df.iloc[-(i+1)]["close"] > df.iloc[-(i+1)]["BBU"] 
                for i in range(lookback)
            )
            
            result = {
                "long": long_condition,
                "short": short_condition,
                "current_price": df.iloc[-1]["close"],
                "upper_band": df.iloc[-1]["BBU"],
                "lower_band": df.iloc[-1]["BBL"],
                "middle_band": df.iloc[-1]["BBM"] if "BBM" in df.columns else None
            }
            
            if long_condition:
                result["message"] = "觸發做多信號"
            elif short_condition:
                result["message"] = "觸發做空信號"
            else:
                result["message"] = "無交易信號"
            
            return result
            
        except Exception as e:
            self.logger.error(f"布林帶信號檢查失敗: {str(e)}")
            raise


# 全域技術指標實例
indicators = TechnicalIndicators()
