"""
重構後的交易機器人主程式
使用物件導向架構和現代化的錯誤處理
"""

import signal
import sys
import time
from typing import Optional

import schedule

from config import config
from logger import get_logger
from trading_bot import TradingBot


class TradingApplication:
    """交易應用程式主類別"""

    def __init__(self):
        self.logger = get_logger("main")
        self.bot: Optional[TradingBot] = None
        self.running = False

        # 設定信號處理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """處理中斷信號"""
        self.logger.info(f"收到信號 {signum}，正在安全關閉...")
        self.shutdown()

    def initialize(self) -> bool:
        """初始化交易機器人"""
        try:
            self.logger.info("正在初始化交易機器人...")

            # 驗證配置
            if not config.validate():
                self.logger.error("配置驗證失敗")
                return False

            # 創建交易機器人
            self.bot = TradingBot()

            self.logger.info("交易機器人初始化完成")
            return True

        except Exception as e:
            self.logger.error(f"初始化失敗: {e!s}")
            return False

    def run_trading_cycle(self):
        """執行交易週期"""
        if not self.bot:
            self.logger.error("交易機器人未初始化")
            return

        try:
            result = self.bot.run_trading_cycle()

            if not result["success"]:
                self.logger.error(f"交易週期執行失敗: {result.get('error', '未知錯誤')}")

        except Exception as e:
            self.logger.error(f"交易週期執行異常: {e!s}")

    def start(self):
        """啟動交易機器人"""
        if not self.initialize():
            self.logger.error("初始化失敗，無法啟動")
            return False

        try:
            self.logger.info("交易機器人啟動中...")
            self.logger.info(f"交易配置: {config.trading.symbol} | 週期: {config.schedule.job_interval_seconds}秒")

            # 設定排程
            schedule.every(config.schedule.job_interval_seconds).seconds.do(self.run_trading_cycle)

            # 立即執行一次
            self.run_trading_cycle()

            self.running = True

            # 主循環
            while self.running:
                schedule.run_pending()
                time.sleep(config.schedule.sleep_interval_seconds)

            self.logger.info("交易機器人已停止")
            return True

        except Exception as e:
            self.logger.error(f"運行時錯誤: {e!s}")
            return False

    def shutdown(self):
        """安全關閉"""
        self.logger.info("正在關閉交易機器人...")
        self.running = False

        if self.bot:
            try:
                # 獲取最終狀態
                status = self.bot.get_performance_summary()
                self.logger.info(
                    f"最終績效: 總獲利={status['stats']['total_profit']:.2f}, "
                    f"勝率={status['stats']['win_rate']:.1f}%, "
                    f"總交易次數={status['stats']['total_trades']}"
                )

                # 如果有倉位，詢問是否緊急平倉
                if status["has_position"]:
                    self.logger.warning("檢測到未平倉位，建議手動處理或使用緊急平倉功能")

            except Exception as e:
                self.logger.error(f"獲取最終狀態失敗: {e!s}")

    def emergency_close(self):
        """緊急平倉"""
        if not self.bot:
            self.logger.error("交易機器人未初始化")
            return False

        self.logger.warning("執行緊急平倉...")
        return self.bot.emergency_close_all()


def main():
    """主函數"""
    app = TradingApplication()

    try:
        success = app.start()
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        app.logger.info("收到鍵盤中斷")
        app.shutdown()
        sys.exit(0)

    except Exception as e:
        app.logger.critical(f"未處理的異常: {e!s}")
        app.shutdown()
        sys.exit(1)


if __name__ == "__main__":
    main()
