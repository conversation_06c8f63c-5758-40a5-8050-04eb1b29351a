"""
日誌系統模組
提供統一的日誌記錄功能
"""
import logging
import logging.handlers
import os
from typing import Optional
from config import config


class TradingLogger:
    """交易日誌記錄器"""
    
    def __init__(self, name: str = "trading", log_file: Optional[str] = None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, config.logging.log_level.upper()))
        
        # 避免重複添加處理器
        if not self.logger.handlers:
            self._setup_handlers(log_file)
    
    def _setup_handlers(self, log_file: Optional[str] = None):
        """設置日誌處理器"""
        formatter = logging.Formatter(config.logging.log_format)
        
        # 控制台處理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 檔案處理器（輪轉）
        log_file = log_file or config.logging.log_file
        if not os.path.exists(os.path.dirname(log_file) or '.'):
            os.makedirs(os.path.dirname(log_file) or '.', exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=config.logging.max_file_size,
            backupCount=config.logging.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def debug(self, message: str, **kwargs):
        """記錄除錯訊息"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """記錄資訊訊息"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """記錄警告訊息"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """記錄錯誤訊息"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """記錄嚴重錯誤訊息"""
        self.logger.critical(message, **kwargs)
    
    def trade_action(self, action: str, symbol: str, side: str, quantity: float, price: float):
        """記錄交易動作"""
        self.info(f"交易動作: {action} | 商品: {symbol} | 方向: {side} | 數量: {quantity} | 價格: {price}")
    
    def position_update(self, status: str, profit: float, wins: int, losses: int):
        """記錄倉位更新"""
        self.info(f"倉位狀態: {status} | 總獲利: {profit} | 勝場: {wins} | 敗場: {losses}")
    
    def market_data(self, symbol: str, price: float, bbu: float, bbl: float):
        """記錄市場數據"""
        self.debug(f"市場數據: {symbol} | 價格: {price} | 上軌: {bbu} | 下軌: {bbl}")
    
    def api_request(self, method: str, url: str, status_code: int, response_time: float):
        """記錄API請求"""
        self.debug(f"API請求: {method} {url} | 狀態碼: {status_code} | 響應時間: {response_time:.3f}s")
    
    def api_error(self, method: str, url: str, error: str):
        """記錄API錯誤"""
        self.error(f"API錯誤: {method} {url} | 錯誤: {error}")


# 全域日誌實例
logger = TradingLogger()


def get_logger(name: str = "trading") -> TradingLogger:
    """獲取日誌記錄器實例"""
    return TradingLogger(name)
