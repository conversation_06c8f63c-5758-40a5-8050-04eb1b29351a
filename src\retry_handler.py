"""
重試機制和錯誤處理模組
提供統一的重試邏輯和錯誤處理功能
"""
import time
import functools
from typing import Callable, Any, Optional, Type, Tuple
import requests

from logger import get_logger


class RetryError(Exception):
    """重試失敗異常"""
    pass


class RetryHandler:
    """重試處理器"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, backoff_factor: float = 2.0):
        """
        初始化重試處理器
        
        Args:
            max_retries: 最大重試次數
            base_delay: 基礎延遲時間（秒）
            max_delay: 最大延遲時間（秒）
            backoff_factor: 退避因子
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.logger = get_logger("retry_handler")
    
    def calculate_delay(self, attempt: int) -> float:
        """計算延遲時間（指數退避）"""
        delay = self.base_delay * (self.backoff_factor ** attempt)
        return min(delay, self.max_delay)
    
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """判斷是否應該重試"""
        if attempt >= self.max_retries:
            return False
        
        # 網路相關錯誤通常可以重試
        if isinstance(exception, (requests.RequestException, ConnectionError, TimeoutError)):
            return True
        
        # API限流錯誤可以重試
        if isinstance(exception, requests.HTTPError):
            if hasattr(exception, 'response') and exception.response is not None:
                status_code = exception.response.status_code
                # 429 (Too Many Requests), 502 (Bad Gateway), 503 (Service Unavailable), 504 (Gateway Timeout)
                if status_code in [429, 502, 503, 504]:
                    return True
        
        return False
    
    def retry(self, func: Callable, *args, **kwargs) -> Any:
        """
        執行函數並在失敗時重試
        
        Args:
            func: 要執行的函數
            *args: 函數參數
            **kwargs: 函數關鍵字參數
            
        Returns:
            函數執行結果
            
        Raises:
            RetryError: 重試次數用盡後仍然失敗
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                result = func(*args, **kwargs)
                
                if attempt > 0:
                    self.logger.info(f"函數 {func.__name__} 在第 {attempt + 1} 次嘗試後成功")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries and self.should_retry(e, attempt):
                    delay = self.calculate_delay(attempt)
                    self.logger.warning(
                        f"函數 {func.__name__} 第 {attempt + 1} 次嘗試失敗: {str(e)}, "
                        f"{delay:.2f}秒後重試"
                    )
                    time.sleep(delay)
                else:
                    break
        
        # 所有重試都失敗了
        self.logger.error(f"函數 {func.__name__} 在 {self.max_retries + 1} 次嘗試後仍然失敗")
        raise RetryError(f"重試失敗: {str(last_exception)}") from last_exception


def retry_on_failure(max_retries: int = 3, base_delay: float = 1.0, 
                    max_delay: float = 60.0, backoff_factor: float = 2.0):
    """
    重試裝飾器
    
    Args:
        max_retries: 最大重試次數
        base_delay: 基礎延遲時間（秒）
        max_delay: 最大延遲時間（秒）
        backoff_factor: 退避因子
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            retry_handler = RetryHandler(max_retries, base_delay, max_delay, backoff_factor)
            return retry_handler.retry(func, *args, **kwargs)
        return wrapper
    return decorator


def safe_execute(func: Callable, default_value: Any = None, 
                log_errors: bool = True) -> Tuple[bool, Any]:
    """
    安全執行函數，捕獲所有異常
    
    Args:
        func: 要執行的函數
        default_value: 發生錯誤時的預設返回值
        log_errors: 是否記錄錯誤
        
    Returns:
        (是否成功, 結果或預設值)
    """
    logger = get_logger("safe_execute")
    
    try:
        result = func()
        return True, result
    except Exception as e:
        if log_errors:
            logger.error(f"函數 {func.__name__} 執行失敗: {str(e)}")
        return False, default_value


class CircuitBreaker:
    """熔斷器模式實現"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        """
        初始化熔斷器
        
        Args:
            failure_threshold: 失敗閾值
            recovery_timeout: 恢復超時時間（秒）
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.logger = get_logger("circuit_breaker")
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        通過熔斷器調用函數
        
        Args:
            func: 要調用的函數
            *args: 函數參數
            **kwargs: 函數關鍵字參數
            
        Returns:
            函數執行結果
            
        Raises:
            Exception: 熔斷器開啟時拋出異常
        """
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "HALF_OPEN"
                self.logger.info("熔斷器進入半開狀態")
            else:
                raise Exception("熔斷器開啟，拒絕請求")
        
        try:
            result = func(*args, **kwargs)
            
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                self.failure_count = 0
                self.logger.info("熔斷器恢復到關閉狀態")
            
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"
                self.logger.warning(f"熔斷器開啟，失敗次數: {self.failure_count}")
            
            raise


# 全域重試處理器實例
default_retry_handler = RetryHandler()

# 全域熔斷器實例
api_circuit_breaker = CircuitBreaker(failure_threshold=5, recovery_timeout=60.0)
