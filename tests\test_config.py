"""
配置管理模組測試
"""
import os
import pytest
from unittest.mock import patch

import sys
sys.path.append('../src')

from src.config import Config, APIConfig, TradingConfig


class TestAPIConfig:
    """API配置測試"""
    
    def test_from_env_success(self):
        """測試從環境變數成功載入配置"""
        with patch.dict(os.environ, {
            'API_KEY': 'test_key',
            'API_SECRET': 'test_secret',
            'API_BASE_URL': 'https://test.api.com',
            'API_RECV_WINDOW': '10000'
        }):
            config = APIConfig.from_env()
            assert config.api_key == 'test_key'
            assert config.api_secret == 'test_secret'
            assert config.base_url == 'https://test.api.com'
            assert config.recv_window == 10000
    
    def test_from_env_missing_keys(self):
        """測試缺少必要環境變數時拋出異常"""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="API_KEY 和 API_SECRET 必須在環境變數中設定"):
                APIConfig.from_env()
    
    def test_from_env_default_values(self):
        """測試使用預設值"""
        with patch.dict(os.environ, {
            'API_KEY': 'test_key',
            'API_SECRET': 'test_secret'
        }, clear=True):
            config = APIConfig.from_env()
            assert config.base_url == "https://api.mexc.com"
            assert config.recv_window == 5000


class TestTradingConfig:
    """交易配置測試"""
    
    def test_from_env_with_custom_values(self):
        """測試自定義環境變數值"""
        with patch.dict(os.environ, {
            'TRADING_SYMBOL': 'ETHUSDT',
            'TRADING_INTERVAL': '5m',
            'TAKE_PROFIT_POINTS': '300',
            'STOP_LOSS_POINTS': '1500',
            'POSITION_SIZE_USD': '100.0',
            'BOLLINGER_PERIOD': '25',
            'BOLLINGER_STD_DEV': '2.5'
        }):
            config = TradingConfig.from_env()
            assert config.symbol == 'ETHUSDT'
            assert config.interval == '5m'
            assert config.take_profit_points == 300
            assert config.stop_loss_points == 1500
            assert config.position_size_usd == 100.0
            assert config.bollinger_period == 25
            assert config.bollinger_std_dev == 2.5
    
    def test_from_env_default_values(self):
        """測試預設值"""
        with patch.dict(os.environ, {}, clear=True):
            config = TradingConfig.from_env()
            assert config.symbol == "BTCUSDC"
            assert config.interval == "1m"
            assert config.take_profit_points == 200
            assert config.stop_loss_points == 1200
            assert config.position_size_usd == 40.0
            assert config.bollinger_period == 20
            assert config.bollinger_std_dev == 2.0


class TestConfig:
    """主配置類別測試"""
    
    def test_validate_success(self):
        """測試配置驗證成功"""
        with patch.dict(os.environ, {
            'API_KEY': 'test_key',
            'API_SECRET': 'test_secret'
        }):
            config = Config()
            assert config.validate() is True
    
    def test_validate_invalid_api_config(self):
        """測試API配置無效時驗證失敗"""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError):
                Config()
    
    def test_validate_invalid_trading_config(self):
        """測試交易配置無效時驗證失敗"""
        with patch.dict(os.environ, {
            'API_KEY': 'test_key',
            'API_SECRET': 'test_secret',
            'TAKE_PROFIT_POINTS': '0',
            'STOP_LOSS_POINTS': '-100'
        }):
            config = Config()
            assert config.validate() is False


if __name__ == '__main__':
    pytest.main([__file__])
