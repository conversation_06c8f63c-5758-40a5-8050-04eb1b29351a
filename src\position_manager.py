"""
倉位管理模組
管理交易倉位、風險控制和績效統計
"""
from dataclasses import dataclass
from typing import Optional, Dict, Any
from enum import Enum
import json
import os

from config import config
from logger import get_logger


class PositionStatus(Enum):
    """倉位狀態枚舉"""
    NO_POSITION = "no_position"
    LONG = "long"
    SHORT = "short"


@dataclass
class Position:
    """倉位資訊"""
    status: PositionStatus = PositionStatus.NO_POSITION
    size: float = 0.0
    entry_price: float = 0.0
    take_profit_price: float = 0.0
    stop_loss_price: float = 0.0
    symbol: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            "status": self.status.value,
            "size": self.size,
            "entry_price": self.entry_price,
            "take_profit_price": self.take_profit_price,
            "stop_loss_price": self.stop_loss_price,
            "symbol": self.symbol
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Position":
        """從字典創建倉位"""
        return cls(
            status=PositionStatus(data.get("status", "no_position")),
            size=data.get("size", 0.0),
            entry_price=data.get("entry_price", 0.0),
            take_profit_price=data.get("take_profit_price", 0.0),
            stop_loss_price=data.get("stop_loss_price", 0.0),
            symbol=data.get("symbol", "")
        )


@dataclass
class TradingStats:
    """交易統計"""
    wins: int = 0
    losses: int = 0
    total_profit: float = 0.0
    total_trades: int = 0
    
    @property
    def win_rate(self) -> float:
        """勝率"""
        if self.total_trades == 0:
            return 0.0
        return self.wins / self.total_trades * 100
    
    @property
    def profit_factor(self) -> float:
        """獲利因子"""
        if self.losses == 0:
            return float('inf') if self.wins > 0 else 0.0
        return self.wins / self.losses
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            "wins": self.wins,
            "losses": self.losses,
            "total_profit": self.total_profit,
            "total_trades": self.total_trades,
            "win_rate": self.win_rate,
            "profit_factor": self.profit_factor
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TradingStats":
        """從字典創建統計"""
        return cls(
            wins=data.get("wins", 0),
            losses=data.get("losses", 0),
            total_profit=data.get("total_profit", 0.0),
            total_trades=data.get("total_trades", 0)
        )


class PositionManager:
    """倉位管理器"""
    
    def __init__(self, state_file: str = "position_state.json"):
        self.logger = get_logger("position_manager")
        self.state_file = state_file
        
        # 載入狀態
        self.position = Position()
        self.stats = TradingStats()
        self.load_state()
    
    def save_state(self):
        """保存狀態到檔案"""
        try:
            state = {
                "position": self.position.to_dict(),
                "stats": self.stats.to_dict()
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2, ensure_ascii=False)
            
            self.logger.debug("倉位狀態已保存")
            
        except Exception as e:
            self.logger.error(f"保存倉位狀態失敗: {str(e)}")
    
    def load_state(self):
        """從檔案載入狀態"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                
                self.position = Position.from_dict(state.get("position", {}))
                self.stats = TradingStats.from_dict(state.get("stats", {}))
                
                self.logger.info("倉位狀態已載入")
            else:
                self.logger.info("未找到狀態檔案，使用預設狀態")
                
        except Exception as e:
            self.logger.error(f"載入倉位狀態失敗: {str(e)}")
            # 使用預設狀態
            self.position = Position()
            self.stats = TradingStats()
    
    def open_position(self, side: str, entry_price: float, size: float, symbol: str = None):
        """開倉"""
        if self.position.status != PositionStatus.NO_POSITION:
            raise ValueError(f"已有倉位存在: {self.position.status.value}")
        
        symbol = symbol or config.trading.symbol
        
        # 設定倉位狀態
        if side.upper() == "BUY":
            self.position.status = PositionStatus.LONG
            self.position.take_profit_price = entry_price + config.trading.take_profit_points
            self.position.stop_loss_price = entry_price - config.trading.stop_loss_points
        elif side.upper() == "SELL":
            self.position.status = PositionStatus.SHORT
            self.position.take_profit_price = entry_price - config.trading.take_profit_points
            self.position.stop_loss_price = entry_price + config.trading.stop_loss_points
        else:
            raise ValueError(f"無效的交易方向: {side}")
        
        self.position.size = size
        self.position.entry_price = entry_price
        self.position.symbol = symbol
        
        self.save_state()
        
        self.logger.position_update(
            status=self.position.status.value,
            profit=self.stats.total_profit,
            wins=self.stats.wins,
            losses=self.stats.losses
        )
        
        self.logger.info(f"開倉成功: {side} {size} {symbol} @ {entry_price}")
        self.logger.info(f"止盈價位: {self.position.take_profit_price}, 止損價位: {self.position.stop_loss_price}")
    
    def close_position(self, exit_price: float, reason: str = "手動平倉") -> bool:
        """平倉"""
        if self.position.status == PositionStatus.NO_POSITION:
            self.logger.warning("嘗試平倉但無倉位存在")
            return False
        
        # 計算盈虧
        if self.position.status == PositionStatus.LONG:
            profit_points = exit_price - self.position.entry_price
        else:  # SHORT
            profit_points = self.position.entry_price - exit_price
        
        # 更新統計
        self.stats.total_trades += 1
        
        if profit_points > 0:
            self.stats.wins += 1
            if reason == "止盈":
                self.stats.total_profit += config.trading.take_profit_points
            else:
                self.stats.total_profit += profit_points
        else:
            self.stats.losses += 1
            if reason == "止損":
                self.stats.total_profit -= config.trading.stop_loss_points
            else:
                self.stats.total_profit += profit_points  # profit_points是負數
        
        self.logger.info(f"平倉成功: {reason} @ {exit_price}, 盈虧點數: {profit_points:.2f}")
        
        # 重置倉位
        old_position = self.position
        self.position = Position()
        
        self.save_state()
        
        self.logger.position_update(
            status=self.position.status.value,
            profit=self.stats.total_profit,
            wins=self.stats.wins,
            losses=self.stats.losses
        )
        
        return True
    
    def check_exit_conditions(self, current_price: float) -> Optional[str]:
        """檢查平倉條件"""
        if self.position.status == PositionStatus.NO_POSITION:
            return None
        
        if self.position.status == PositionStatus.LONG:
            if current_price >= self.position.take_profit_price:
                return "止盈"
            elif current_price <= self.position.stop_loss_price:
                return "止損"
        elif self.position.status == PositionStatus.SHORT:
            if current_price <= self.position.take_profit_price:
                return "止盈"
            elif current_price >= self.position.stop_loss_price:
                return "止損"
        
        return None
    
    def calculate_position_size(self, current_price: float) -> float:
        """計算倉位大小"""
        return round(config.trading.position_size_usd / current_price, 5)
    
    def get_status_summary(self) -> Dict[str, Any]:
        """獲取狀態摘要"""
        return {
            "position": self.position.to_dict(),
            "stats": self.stats.to_dict(),
            "has_position": self.position.status != PositionStatus.NO_POSITION
        }
