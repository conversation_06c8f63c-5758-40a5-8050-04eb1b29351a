"""
配置管理模組
統一管理所有系統配置參數
"""
import os
from dataclasses import dataclass
from typing import Optional
import dotenv

# 載入環境變數
dotenv.load_dotenv()


@dataclass
class APIConfig:
    """API 配置"""
    api_key: str
    api_secret: str
    base_url: str = "https://api.mexc.com"
    recv_window: int = 5000
    
    @classmethod
    def from_env(cls) -> "APIConfig":
        """從環境變數載入API配置"""
        api_key = os.getenv("API_KEY")
        api_secret = os.getenv("API_SECRET")
        
        if not api_key or not api_secret:
            raise ValueError("API_KEY 和 API_SECRET 必須在環境變數中設定")
        
        return cls(
            api_key=api_key,
            api_secret=api_secret,
            base_url=os.getenv("API_BASE_URL", "https://api.mexc.com"),
            recv_window=int(os.getenv("API_RECV_WINDOW", "5000"))
        )


@dataclass
class TradingConfig:
    """交易配置"""
    symbol: str = "BTCUSDC"
    interval: str = "1m"
    take_profit_points: int = 200
    stop_loss_points: int = 1200
    position_size_usd: float = 40.0
    bollinger_period: int = 20
    bollinger_std_dev: float = 2.0
    
    @classmethod
    def from_env(cls) -> "TradingConfig":
        """從環境變數載入交易配置"""
        return cls(
            symbol=os.getenv("TRADING_SYMBOL", "BTCUSDC"),
            interval=os.getenv("TRADING_INTERVAL", "1m"),
            take_profit_points=int(os.getenv("TAKE_PROFIT_POINTS", "200")),
            stop_loss_points=int(os.getenv("STOP_LOSS_POINTS", "1200")),
            position_size_usd=float(os.getenv("POSITION_SIZE_USD", "40.0")),
            bollinger_period=int(os.getenv("BOLLINGER_PERIOD", "20")),
            bollinger_std_dev=float(os.getenv("BOLLINGER_STD_DEV", "2.0"))
        )


@dataclass
class ScheduleConfig:
    """排程配置"""
    job_interval_seconds: int = 30
    sleep_interval_seconds: int = 2
    
    @classmethod
    def from_env(cls) -> "ScheduleConfig":
        """從環境變數載入排程配置"""
        return cls(
            job_interval_seconds=int(os.getenv("JOB_INTERVAL_SECONDS", "30")),
            sleep_interval_seconds=int(os.getenv("SLEEP_INTERVAL_SECONDS", "2"))
        )


@dataclass
class LoggingConfig:
    """日誌配置"""
    log_level: str = "INFO"
    log_file: str = "trading.log"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    
    @classmethod
    def from_env(cls) -> "LoggingConfig":
        """從環境變數載入日誌配置"""
        return cls(
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_file=os.getenv("LOG_FILE", "trading.log"),
            log_format=os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", str(10 * 1024 * 1024))),
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5"))
        )


class Config:
    """主配置類別"""
    
    def __init__(self):
        self.api = APIConfig.from_env()
        self.trading = TradingConfig.from_env()
        self.schedule = ScheduleConfig.from_env()
        self.logging = LoggingConfig.from_env()
    
    def validate(self) -> bool:
        """驗證配置是否有效"""
        try:
            # 驗證API配置
            if not self.api.api_key or not self.api.api_secret:
                raise ValueError("API金鑰配置無效")
            
            # 驗證交易配置
            if self.trading.take_profit_points <= 0 or self.trading.stop_loss_points <= 0:
                raise ValueError("止盈止損點數必須大於0")
            
            if self.trading.position_size_usd <= 0:
                raise ValueError("倉位大小必須大於0")
            
            # 驗證布林帶配置
            if self.trading.bollinger_period <= 0 or self.trading.bollinger_std_dev <= 0:
                raise ValueError("布林帶參數必須大於0")
            
            return True
            
        except Exception as e:
            print(f"配置驗證失敗: {e}")
            return False


# 全域配置實例
config = Config()
