"""
MEXC API 客戶端模組
統一處理所有與MEXC API的互動
"""

import hashlib
import hmac
import time
import urllib.parse
from typing import Any, Dict, Optional

import numpy as np
import pandas as pd
import requests

from config import config
from logger import get_logger
from retry_handler import retry_on_failure


class APIError(Exception):
    """API錯誤異常"""

    pass


class MEXCAPIClient:
    """MEXC API 客戶端"""

    def __init__(self):
        self.api_key = config.api.api_key
        self.api_secret = config.api.api_secret
        self.base_url = config.api.base_url
        self.recv_window = config.api.recv_window
        self.logger = get_logger("api_client")

        # 請求會話
        self.session = requests.Session()
        self.session.headers.update({"X-MEXC-APIKEY": self.api_key, "Content-Type": "application/json"})

    def _generate_signature(self, params: Dict[str, Any], endpoint_type: str = "klines") -> str:
        """生成API簽名"""
        if endpoint_type == "klines":
            param_list = [
                ("symbol", params.get("symbol")),
                ("interval", params.get("interval")),
                ("timestamp", params.get("timestamp")),
                ("recvWindow", params.get("recvWindow")),
            ]
        elif endpoint_type == "order":
            param_list = [
                ("symbol", params.get("symbol")),
                ("side", params.get("side")),
                ("type", params.get("type")),
                ("quantity", params.get("quantity")),
                ("quoteOrderQty", params.get("quoteOrderQty")),
                ("newClientOrderId", params.get("newClientOrderId")),
                ("timestamp", params.get("timestamp")),
                ("recvWindow", params.get("recvWindow")),
            ]
        else:
            raise ValueError(f"不支援的端點類型: {endpoint_type}")

        # 過濾空值並轉換為字串
        final_params = [(k, str(v)) for k, v in param_list if v is not None]

        # 生成查詢字串
        query_string = urllib.parse.urlencode(final_params)

        # 生成HMAC SHA256簽名
        signature = hmac.new(self.api_secret.encode("utf-8"), query_string.encode("utf-8"), hashlib.sha256).hexdigest()

        return signature.lower()

    def _make_request(
        self, method: str, endpoint: str, params: Dict[str, Any], endpoint_type: str = "klines"
    ) -> Dict[str, Any]:
        """發送API請求"""
        url = f"{self.base_url}{endpoint}"

        # 添加時間戳
        params["timestamp"] = int(time.time() * 1000)
        params["recvWindow"] = self.recv_window

        # 生成簽名
        signature = self._generate_signature(params, endpoint_type)
        params["signature"] = signature

        start_time = time.time()

        try:
            if method.upper() == "GET":
                response = self.session.get(url, params=params)
            elif method.upper() == "POST":
                response = self.session.post(url, params=params)
            else:
                raise ValueError(f"不支援的HTTP方法: {method}")

            response_time = time.time() - start_time
            self.logger.api_request(method, url, response.status_code, response_time)

            if response.status_code == 200:
                return response.json()
            else:
                error_msg = f"API請求失敗: {response.status_code} - {response.text}"
                self.logger.api_error(method, url, error_msg)
                raise APIError(error_msg)

        except requests.RequestException as e:
            error_msg = f"網路請求錯誤: {e!s}"
            self.logger.api_error(method, url, error_msg)
            raise APIError(error_msg)

    @retry_on_failure(max_retries=3, base_delay=1.0)
    def get_klines(self, symbol: Optional[str] = None, interval: Optional[str] = None) -> pd.DataFrame:
        """獲取K線數據"""
        symbol = symbol or config.trading.symbol
        interval = interval or config.trading.interval

        params = {"symbol": symbol, "interval": interval}

        try:
            data = self._make_request("GET", "/api/v3/klines", params, "klines")

            if not data:
                raise APIError("未獲取到K線數據")

            # 轉換為DataFrame
            columns = ["時間", "開盤", "高", "低", "close", "成交量", "收盤時間", "成交額"]
            df = pd.DataFrame(data, columns=columns)

            # 轉換數據類型
            df = df.astype(np.float64)

            self.logger.debug(f"成功獲取K線數據: {symbol} {interval}, 共{len(df)}筆")
            return df

        except Exception as e:
            self.logger.error(f"獲取K線數據失敗: {e!s}")
            raise

    @retry_on_failure(max_retries=2, base_delay=0.5)
    def place_order(
        self, side: str, quantity: float, symbol: Optional[str] = None, order_type: str = "MARKET"
    ) -> Dict[str, Any]:
        """下單"""
        symbol = symbol or config.trading.symbol

        params = {
            "symbol": symbol,
            "side": side.upper(),
            "type": order_type.upper(),
            "quantity": quantity,
            "quoteOrderQty": None,
            "newClientOrderId": None,
        }

        try:
            result = self._make_request("POST", "/api/v3/order", params, "order")

            self.logger.trade_action(
                action="下單",
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=0,  # 市價單沒有固定價格
            )

            return result

        except Exception as e:
            self.logger.error(f"下單失敗: {side} {quantity} {symbol} - {e!s}")
            raise
